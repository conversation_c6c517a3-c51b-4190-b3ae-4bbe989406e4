<template>
  <div>
    <div class="p-horizontal-divider my-0 d-none d-md-block" ref="metasTerapeuticasFraming"></div>

      <Transition name="carousel-fade">
        <div v-if="imagens.length > 0"
          class="p-1 flex-row img-carousel-container d-none d-md-flex"
          v-viewer="{ title: [1, (image, imageData) => `${image.alt}`], hide: resumeCarousel }"
          ref="carouselViewer"
          :class="{ 'dimmed-carousel': planejamentoTab === 'imagens' }"
        >
        <div class="carousel-controls">
          <button class="carousel-control" @click="toggleCarousel" :title="isPaused ? 'Iniciar' : 'Pausar'">
            <font-awesome-icon :icon="['fas', isPaused ? 'play' : 'pause']" />
          </button>
          <button class="carousel-control" @click="toggleCarouselVisibility" :title="isCarouselHidden ? 'Mostrar' : 'Esconder'">
            <font-awesome-icon :icon="['fas', isCarouselHidden ? 'chevron-down' : 'chevron-up']" />
          </button>
        </div>
        <!-- Botão para restaurar o carousel quando estiver escondido -->
        <Transition name="fade">
          <div v-if="isCarouselHidden" class="carousel-restore-container">
            <button class="carousel-restore-button" @click="toggleCarouselVisibility" title="Mostrar documentação">
              <font-awesome-icon :icon="['fas', 'images']" class="me-2" />
              Mostrar documentação
            </button>
          </div>
        </Transition>

        <div
          class="img-carousel-inner"
          :style="{
            animationPlayState: isPaused ? 'paused' : 'running',
            display: isCarouselHidden ? 'none' : 'flex'
          }"
        >
          <div
            v-for="imagem in imagens"
            :key="imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
          <div
            v-for="imagem in imagens"
            :key="'dup-' + imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
        </div>
      </div>
      </Transition>

    <div class="my-0" ref="metasTerapeuticasFraming" style="margin-top: 0px !important; background:rgb(228, 228, 228); height: 1px;"></div>

    <!-- Navegação de Fases Elegante -->
    <div class="phases-navigation-wrapper mb-4">
      <div class="phases-navigation">
        <!-- Documentação Inicial -->
        <div class="phase-step"
             :class="getStepClasses('imagens')"
             @click="selectPlanejamentoTab('imagens')">
          <div class="phase-content">
            <div class="phase-icon">
              <font-awesome-icon
                :icon="getStepIcon('imagens')"
              />
            </div>
            <div class="phase-title">Documentação inicial</div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Análise -->
        <div class="phase-step"
             :class="getStepClasses('analise')"
             @click="canAccessStep('analise') && selectPlanejamentoTab('analise')">
          <div class="phase-content">
            <div class="phase-icon">
              <font-awesome-icon
                :icon="getStepIcon('analise')"
              />
            </div>
            <div class="phase-title">Análise clínica</div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Diagnóstico -->
        <div class="phase-step"
             :class="getStepClasses('diagnostico')"
             @click="canAccessStep('diagnostico') && selectPlanejamentoTab('diagnostico')">
          <div class="phase-content">
            <div class="phase-icon">
              <font-awesome-icon
                :icon="getStepIcon('diagnostico')"
              />
            </div>
            <div class="phase-title">Diagnóstico médico</div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Plano de Tratamento -->
        <div class="phase-step last-step"
             :class="getStepClasses('planoTratamento')"
             @click="canAccessStep('planoTratamento') && selectPlanejamentoTab('planoTratamento')">
          <div class="phase-content">
            <div class="phase-icon">
              <font-awesome-icon
                :icon="getStepIcon('planoTratamento')"
              />
            </div>
            <div class="phase-title">Plano de tratamento</div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-horizontal-divider"></div>

    <Transition>
      <Imagens
        v-show="planejamentoTab === 'imagens'"
        :paciente="paciente"
        mode="diagnostic"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Analise
        v-if="planejamentoTab === 'analise'"
        :paciente="paciente"
        :detalhesClinicos="detalhesClinicos"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Diagnostico
        v-show="planejamentoTab === 'diagnostico'"
        :paciente="paciente"
        :diagnostico="paciente.diagnostico"
        :prognostico="paciente.prognostico"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <PlanoTratamento
        v-show="planejamentoTab === 'planoTratamento'"
        :paciente="paciente"
        @pacienteChange="$emit('pacienteChange')"
        @edit-mode-active="handleEditModeActive"
      />
    </Transition>


  </div>
</template>

<style scoped>
/* Transition effects ultra suaves */
.v-enter-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.v-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.6, 1);
}

.v-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.98);
  filter: blur(2px);
}

.v-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(1.02);
  filter: blur(1px);
}

/* Animação especial para mudança de fases */
@keyframes phaseTransition {
  0% {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
    filter: blur(3px);
  }
  50% {
    opacity: 0.7;
    transform: translateX(5px) scale(1.02);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

.phase-content-enter {
  animation: phaseTransition 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Estilos da Navegação de Fases Ultra Moderna */
.phases-navigation-wrapper {
  padding: 0;
  margin: 16px 0;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.phases-navigation-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.9) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.phases-navigation-wrapper:hover::before {
  opacity: 1;
}

.phases-navigation {
  display: flex;
  height: 90px;
  position: relative;
  z-index: 1;
}

/* Linha de progresso sutil no fundo */
.phases-navigation::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(233, 236, 239, 0.6) 0%,
    rgba(222, 226, 230, 0.4) 100%);
  z-index: 0;
}

.phases-navigation::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(76, 175, 80, 0.8) 0%,
    rgba(33, 150, 243, 0.6) 50%,
    rgba(25, 118, 210, 0.8) 100%);
  transition: width 1s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 1;
  width: var(--progress-width, 0%);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.4);
}

.phase-step {
  flex: 1;
  position: relative;
  cursor: pointer;
  background: linear-gradient(135deg,
    rgba(248, 249, 250, 0.95) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(233, 236, 239, 0.7) 100%);
  border-right: 1px solid rgba(222, 226, 230, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  backdrop-filter: blur(10px);
}

.phase-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent);
  transition: left 0.8s ease;
  z-index: 1;
}

.phase-step:hover::before {
  left: 100%;
}

.phase-step:last-child {
  border-right: none;
}

/* Chevron (seta) ultra moderna entre as fases */
.phase-chevron {
  position: absolute;
  top: 0;
  right: -18px;
  width: 36px;
  height: 100%;
  z-index: 3;
  pointer-events: none;
  filter: drop-shadow(2px 0 4px rgba(0, 0, 0, 0.1));
}

.phase-chevron::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: inherit;
  transform: skewX(25deg);
  transform-origin: bottom left;
  border-right: 1px solid rgba(222, 226, 230, 0.4);
  transition: all 0.3s ease;
}

.phase-chevron::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: inherit;
  transform: skewX(-25deg);
  transform-origin: top left;
  border-right: 1px solid rgba(222, 226, 230, 0.4);
  transition: all 0.3s ease;
}

.phase-step.last-step .phase-chevron {
  display: none;
}

.phase-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  z-index: 2;
  position: relative;
  padding: 0 24px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.phase-icon {
  font-size: 28px;
  color: rgba(108, 117, 125, 0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin-bottom: 2px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  position: relative;
}

.phase-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  z-index: -1;
}

.phase-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(73, 80, 87, 0.9);
  text-align: center;
  line-height: 1.2;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.3px;
}

/* Estados das fases - Ultra Modernos */

/* Estado Ativo - Azul Vibrante */
.phase-step.active {
  background: linear-gradient(135deg,
    rgba(33, 150, 243, 0.25) 0%,
    rgba(25, 118, 210, 0.15) 50%,
    rgba(13, 71, 161, 0.1) 100%);
  transform: scale(1.05) translateY(-3px);
  box-shadow:
    0 12px 40px rgba(33, 150, 243, 0.25),
    0 4px 16px rgba(25, 118, 210, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 0 0 2px rgba(33, 150, 243, 0.3);
  animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% {
    box-shadow:
      0 12px 40px rgba(33, 150, 243, 0.25),
      0 4px 16px rgba(25, 118, 210, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 0 0 2px rgba(33, 150, 243, 0.3);
  }
  100% {
    box-shadow:
      0 16px 50px rgba(33, 150, 243, 0.35),
      0 6px 20px rgba(25, 118, 210, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      inset 0 0 0 2px rgba(33, 150, 243, 0.4);
  }
}

.phase-step.active .phase-icon {
  color: #1565C0;
  font-size: 32px;
  text-shadow: 0 3px 8px rgba(25, 118, 210, 0.4);
  animation: iconPulse 2s ease-in-out infinite;
}

.phase-step.active .phase-icon::after {
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.3) 0%, transparent 70%);
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.phase-step.active .phase-title {
  color: #1565C0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}

.phase-step.active .phase-chevron::before,
.phase-step.active .phase-chevron::after {
  background: linear-gradient(135deg,
    rgba(33, 150, 243, 0.25) 0%,
    rgba(25, 118, 210, 0.15) 50%,
    rgba(13, 71, 161, 0.1) 100%);
  border-right-color: rgba(33, 150, 243, 0.4);
}

/* Estado Completo - Verde Elegante */
.phase-step.completed {
  background: linear-gradient(135deg,
    rgba(76, 175, 80, 0.2) 0%,
    rgba(56, 142, 60, 0.12) 50%,
    rgba(27, 94, 32, 0.08) 100%);
  box-shadow:
    0 8px 32px rgba(76, 175, 80, 0.2),
    0 2px 12px rgba(56, 142, 60, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 0 0 1px rgba(76, 175, 80, 0.2);
}

.phase-step.completed .phase-icon {
  color: #2E7D32;
  font-size: 28px;
  text-shadow: 0 2px 6px rgba(56, 142, 60, 0.3);
}

.phase-step.completed .phase-icon::after {
  width: 50px;
  height: 50px;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.2) 0%, transparent 70%);
}

.phase-step.completed .phase-title {
  color: #2E7D32;
  font-weight: 650;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.7);
}

.phase-step.completed .phase-chevron::before,
.phase-step.completed .phase-chevron::after {
  background: linear-gradient(135deg,
    rgba(76, 175, 80, 0.2) 0%,
    rgba(56, 142, 60, 0.12) 50%,
    rgba(27, 94, 32, 0.08) 100%);
  border-right-color: rgba(76, 175, 80, 0.3);
}

/* Estado Desabilitado - Cinza Sofisticado */
.phase-step.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: linear-gradient(135deg,
    rgba(206, 212, 218, 0.4) 0%,
    rgba(173, 181, 189, 0.3) 50%,
    rgba(134, 142, 150, 0.2) 100%);
  filter: grayscale(0.3);
}

.phase-step.disabled .phase-icon {
  color: rgba(108, 117, 125, 0.5);
  filter: none;
}

.phase-step.disabled .phase-title {
  color: rgba(73, 80, 87, 0.5);
}

/* Efeitos de Hover Ultra Sofisticados */
.phase-step:hover:not(.disabled):not(.active) {
  transform: scale(1.03) translateY(-4px);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 249, 250, 0.9) 50%,
    rgba(233, 236, 239, 0.85) 100%);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.phase-step:hover:not(.disabled) .phase-icon {
  transform: scale(1.15) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.phase-step:hover:not(.disabled) .phase-icon::after {
  width: 55px;
  height: 55px;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.2) 0%, transparent 70%);
}

.phase-step:hover:not(.disabled) .phase-title {
  transform: translateY(-3px);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.phase-step:hover:not(.disabled) .phase-chevron::before,
.phase-step:hover:not(.disabled) .phase-chevron::after {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* Responsividade Ultra Moderna */
@media (max-width: 768px) {
  .phases-navigation-wrapper {
    margin: 12px 0;
    border-radius: 16px;
    box-shadow:
      0 6px 24px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .phases-navigation {
    height: 75px;
  }

  .phase-content {
    gap: 6px;
    padding: 0 16px;
  }

  .phase-icon {
    font-size: 22px;
    margin-bottom: 1px;
  }

  .phase-step.active .phase-icon {
    font-size: 26px;
    animation: iconPulseMobile 2s ease-in-out infinite;
  }

  @keyframes iconPulseMobile {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.08); }
  }

  .phase-step.active {
    transform: scale(1.02) translateY(-2px);
    box-shadow:
      0 8px 28px rgba(33, 150, 243, 0.2),
      0 3px 12px rgba(25, 118, 210, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      inset 0 0 0 1px rgba(33, 150, 243, 0.25);
  }

  .phase-title {
    font-size: 0.75rem;
    line-height: 1.1;
    letter-spacing: 0.2px;
  }

  .phase-chevron {
    right: -14px;
    width: 28px;
  }

  .phase-step:hover:not(.disabled):not(.active) {
    transform: scale(1.02) translateY(-2px);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.12),
      0 3px 12px rgba(0, 0, 0, 0.08);
  }

  .phase-step:hover:not(.disabled) .phase-icon {
    transform: scale(1.1) rotate(3deg);
  }

  .phase-step:hover:not(.disabled) .phase-title {
    transform: translateY(-2px);
  }

  /* Reduzir animações em dispositivos móveis para melhor performance */
  .phase-step.active {
    animation: none;
  }

  .phase-step::before {
    transition: left 0.6s ease;
  }
}

.planejamento-content {
  padding: 20px;
  padding-top: 5px;
}

.img-carousel-container {
  overflow-y: hidden;
  overflow-x: hidden; /* hide scrollbar */
  background: #f8f9fa;
  border-width: 0px 1px 0px 1px;
  border-style: solid;
  border-color: #e2e2e2;
  gap: 0px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
  padding: 10px 0;
}

.img-carousel-container.dimmed-carousel {
  background: rgba(0, 0, 0, 0.03);
  position: relative;
}

.img-carousel-container.dimmed-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(1px);
  z-index: 5;
  pointer-events: none;
}

.carousel-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.carousel-control {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #dee2e6;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-control:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.carousel-restore-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  min-height: 60px;
}

.carousel-restore-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.85rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.carousel-restore-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: #2152ff;
}

.img-carousel-inner {
  display: flex;
  flex-wrap: nowrap;
  animation: scroll-left 40s linear infinite;
  padding: 5px 0;
}

.img-carousel-item {
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  flex-shrink: 0;
  margin-right: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.img-carousel-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent);
  color: white;
  padding: 12px 8px 8px;
  font-size: 0.7rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.img-carousel-info .img-time {
  font-weight: 600;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.img-carousel-info .img-desc {
  font-size: 0.65rem;
  opacity: 0.9;
  font-style: italic;
  line-height: 1.2;
  max-height: 2.4em;
  overflow: hidden;
}

.img-carousel-item:hover .img-carousel-info {
  transform: translateY(0);
}

.img-carousel-item img {
  width: 160px;
  height: auto;
  aspect-ratio: 9/6;
  object-fit: cover;
  filter: brightness(90%);
  transition: all 0.3s ease;
}

.img-carousel-item:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.img-carousel-item:hover img {
  filter: brightness(110%);
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.empty-photo {
  width: 300px;
  height: 96px;
}

/* Carousel fade transition */
.carousel-fade-enter-active,
.carousel-fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.carousel-fade-enter-from,
.carousel-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Fade transition for buttons */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}



</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import Analise from "./Planejamento/components/Analise.vue";
import Diagnostico from "./Planejamento/components/Diagnostico.vue";

import Imagens from "./Planejamento/components/Imagens.vue";
import PlanoTratamento from "./Planejamento/components/PlanoTratamento.vue";
import { getImageDescription } from "@/helpers/utils";
import setNavPills from "@/assets/js/nav-pills.js";

const items = [];
var isEditing = [];

export default {
  name: "planejamento",
  props: {
    paciente: {
      type: Object,
    },
  },
  data() {
    return {
      isEditing,
      items,
      planejamentoTab: "imagens",

      isPaused: false,
      isCarouselHidden: false,
      editModeActive: false,
    };
  },
  methods: {
    getImageDescription,
    selectPlanejamentoTab(tab) {
      // Atualizar a variável de estado
      this.planejamentoTab = tab;

      // Handle carousel visibility based on selected tab
      if (tab === 'imagens') {
        // When on Documentação inicial tab, dim the carousel but don't hide it completely
        // This prevents the layout from jumping when switching tabs
      } else {
        // When on other tabs, ensure carousel is visible
        this.isCarouselHidden = false;
      }

      // Atualizar a largura da barra de progresso
      this.updateProgressWidth();
    },

    updateProgressWidth() {
      this.$nextTick(() => {
        const progressWidth = this.calculateProgressWidth();
        const navigationElement = document.querySelector('.phases-navigation');
        if (navigationElement) {
          navigationElement.style.setProperty('--progress-width', `${progressWidth}%`);
        }
      });
    },

    calculateProgressWidth() {
      const steps = ['imagens', 'analise', 'diagnostico', 'planoTratamento'];
      const currentIndex = steps.indexOf(this.planejamentoTab);
      const completedSteps = steps.filter(step => this.isStepCompleted(step)).length;

      // Calcula o progresso baseado na combinação de etapas completas e etapa atual
      const baseProgress = (completedSteps / steps.length) * 100;
      const currentProgress = ((currentIndex + 1) / steps.length) * 100;

      // Retorna o maior valor entre progresso completo e progresso atual
      return Math.max(baseProgress, currentProgress * 0.8); // 0.8 para não chegar a 100% até estar realmente completo
    },

    // Métodos para a barra de progresso
    getStepClasses(step) {
      const classes = [];

      if (this.planejamentoTab === step) {
        classes.push('active');
      }

      if (this.isStepCompleted(step)) {
        classes.push('completed');
      }

      if (!this.canAccessStep(step)) {
        classes.push('disabled');
      }

      return classes;
    },

    getStepIcon(step) {
      const icons = {
        'imagens': ['fas', 'image'],
        'analise': ['fas', 'magnifying-glass'],
        'diagnostico': ['fas', 'book-medical'],
        'planoTratamento': ['fas', 'file-pen']
      };

      // Se a etapa está completa, mostra um check
      if (this.isStepCompleted(step)) {
        return ['fas', 'check'];
      }

      return icons[step] || ['fas', 'circle'];
    },

    canAccessStep(step) {
      const stepOrder = ['imagens', 'analise', 'diagnostico', 'planoTratamento'];
      const currentIndex = stepOrder.indexOf(step);

      // Sempre pode acessar a primeira etapa
      if (currentIndex === 0) return true;

      // Para outras etapas, verifica se a anterior está completa
      const previousStep = stepOrder[currentIndex - 1];
      return this.isStepCompleted(previousStep);
    },

    isStepCompleted(step) {
      switch (step) {
        case 'imagens':
          // Considera completa se tem pelo menos algumas imagens de diagnóstico
          return this.safePatientImages.length > 0;
        case 'analise':
          // Considera completa se tem análises preenchidas (simplificado)
          return this.paciente.analises && Object.keys(this.paciente.analises).length > 0;
        case 'diagnostico':
          // Considera completa se tem diagnóstico
          return this.paciente.diagnostico && this.paciente.diagnostico.trim().length > 0;
        case 'planoTratamento':
          // Considera completa se tem plano de tratamento
          return this.paciente.plano_tratamento && this.paciente.plano_tratamento.trim().length > 0;
        default:
          return false;
      }
    },
    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
    },
    /**
     * Pause the carousel of images when an image is clicked. This helps
     * prevent the user from accidentally advancing to the next image while
     * they are viewing an image.
     */
    pauseCarousel() {
      this.isPaused = true;
    },
    resumeCarousel() {
      this.isPaused = false;
    },
    /**
     * Toggle the carousel between paused and playing states
     */
    toggleCarousel() {
      this.isPaused = !this.isPaused;
    },
    /**
     * Toggle the visibility of the carousel
     */
    toggleCarouselVisibility() {
      this.isCarouselHidden = !this.isCarouselHidden;
    },
    /**
     * Truncate a description to a specified length and add ellipsis if needed
     */
    truncateDescription(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },


    // Método para lidar com o evento edit-mode-active do componente PlanoTratamento
    handleEditModeActive(isActive) {
      this.editModeActive = isActive;
      // Propagar o evento para o componente pai
      this.$emit('edit-mode-active', isActive);
    },
  },
  components: {
    Analise,
    Diagnostico,
    Imagens,
    PlanoTratamento,
    MaterialInput,
  },
  computed: {
    imagens() {
      const allImages = this.paciente.imagens.filter((imagem) => imagem.dir !== "profile_pic");

      // Separar imagens de diagnóstico das regulares
      const diagnosticImages = allImages.filter(img => img.is_diagnostico === true || img.is_diagnostico === 1);
      const regularImages = allImages.filter(img => img.is_diagnostico !== true && img.is_diagnostico !== 1);

      // Ordenar imagens de diagnóstico: Extra-bucais, Intra-bucais, Radiografias
      const orderedDiagnosticImages = [];

      // 1. Extra-bucais
      const extraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('extra_');
      });
      orderedDiagnosticImages.push(...extraBucais);

      // 2. Intra-bucais
      const intraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('intra_');
      });
      orderedDiagnosticImages.push(...intraBucais);

      // 3. Radiografias (por último)
      const radiografias = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('radio_');
      });
      orderedDiagnosticImages.push(...radiografias);

      // Retornar imagens de diagnóstico ordenadas + imagens regulares
      return [...orderedDiagnosticImages, ...regularImages];
    },

    // Computed properties para a barra de progresso
    safePatientImages() {
      return this.paciente?.imagens?.filter(img =>
        img.is_diagnostico === true || img.is_diagnostico === 1
      ) || [];
    },


    detalhesClinicos() {
      return this.paciente.detalhes_paciente
        ? this.paciente.detalhes_paciente.filter((detalhe) => detalhe.tipo == "clinico")
        : [];
    },
    // Computed property to determine if carousel should be dimmed
    shouldDimCarousel() {
      return this.planejamentoTab === 'imagens';
    },
    ultimaFase() {
      return this.paciente.fases_tratamento[this.paciente.fases_tratamento.length - 1]
        .data_fim;
    },
  },

  mounted() {
    // Add a global reference to the component for debugging
    window.planejamentoComponent = this;

    this.$nextTick(() => {
      // Garantir que a tab inicial (documentação inicial) esteja ativa
      this.planejamentoTab = 'imagens';

      // Inicializar a barra de progresso
      this.updateProgressWidth();

      // Não inicializar o nav-pills para usar o estilo de tab azul em vez do moving-tab

      // Initialize carousel state based on current tab
      this.isPaused = false;
      this.isCarouselHidden = this.planejamentoTab === 'imagens'; // 'imagens' é o valor da variável, mas representa a tab "Documentação inicial"

      // Add event listener to watch for tab changes and update carousel state
      this.$watch('planejamentoTab', (newTab) => {
        // When switching to Documentação inicial tab, dim the carousel
        if (newTab === 'imagens') {
          // Optional: automatically hide carousel when on Documentação inicial tab
          // this.isCarouselHidden = true;
        } else {
          // When switching away from Documentação inicial tab, ensure carousel is visible
          this.isCarouselHidden = false;
        }
      });

      // Watch para atualizar progresso quando dados do paciente mudarem
      this.$watch('paciente', () => {
        this.updateProgressWidth();
      }, { deep: true });
    });
  },
  beforeUnmount() {},
};
</script>
